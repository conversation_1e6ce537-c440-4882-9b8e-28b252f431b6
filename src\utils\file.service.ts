// Imports
import { HttpStatus, Injectable } from '@nestjs/common';
import * as excel from 'excel4node';
import axios from 'axios';
import * as fs from 'fs';
import { Env } from 'src/config/env';
import { Storage } from '@google-cloud/storage';
import { ObjectStorageClient, requests } from 'oci-objectstorage';
import { Region, SimpleAuthenticationDetailsProvider } from 'oci-common';
import { kCryptPath } from 'src/constant/path';
import { CLOUD_FOLDER, kFonts, kMimeTypes } from 'src/constant/objects';
import { RedisService } from 'src/database/redis/redis.service';
import {
  kCloudService,
  kGoogle,
  kMediaImages,
  kRupee,
} from 'src/constant/strings';
import { GlobalServices } from 'src/constant/global';
import {
  HTTPError,
  raiseBadRequest,
  raiseParamMissing,
} from 'src/config/error';
import { DateService } from './date.service';

@Injectable()
export class FileService {
  googleStorage;
  objectStorageClient;
  constructor(
    private readonly dateService: DateService,
    private readonly redisService: RedisService,
  ) {
    this.googleStorage = new Storage({
      keyFilename: Env.cloud.google.appCredentials,
    });

    ///oracle cloud configuration
    const keyFilePath = kCryptPath.oraclePrivateKey;
    const privateKey = fs
      .readFileSync(keyFilePath, 'utf8')
      .replace(/\r\n/g, '\n')
      .trim();
    const provider = new SimpleAuthenticationDetailsProvider(
      Env.cloud.oracle.tenancy,
      Env.cloud.oracle.user,
      Env.cloud.oracle.fingerprint,
      privateKey,
      null,
      Region.AP_MUMBAI_1,
    );

    this.objectStorageClient = new ObjectStorageClient({
      authenticationDetailsProvider: provider,
    });
  }

  private getBuffer(filePath: string) {
    if (!filePath) raiseParamMissing('filePath');
    return fs.readFileSync(filePath);
  }

  removeFile(filePath: string) {
    if (!filePath) raiseParamMissing('filePath');
    if (fs.existsSync(filePath)) fs.unlinkSync(filePath);
  }

  async uploadFile(
    filePath: string,
    folderName = CLOUD_FOLDER.default,
    extension?: string,
    fileName?: string,
    bucketName?: string,
  ): Promise<any> {
    const service =
      (await this.redisService.getString(kCloudService)) ??
      GlobalServices.CLOUD_SERVICE;
    if (kGoogle == service)
      return this.uploadGoogleCloudFile(
        filePath,
        folderName,
        extension,
        fileName,
      );
    else
      return this.uploadOracleCloudFile(
        filePath,
        folderName,
        extension,
        bucketName,
      );
  }

  private async uploadGoogleCloudFile(
    filePath: string,
    folderName?: string,
    extension?: string,
    fileName?: string,
  ): Promise<any> {
    try {
      const bufferData = this.getBuffer(filePath);
      const url = await new Promise(async (resolve, reject) => {
        const bucket = await this.googleStorage.bucket(
          Env.cloud.google.bucketName,
        );
        let nameData: string = Date.now().toString();
        nameData = nameData.concat('.');
        nameData = nameData.concat(extension ?? '');

        let file = bucket.file(
          (folderName ?? kMediaImages) + '/' + (fileName ?? nameData),
        );

        if (fileName) {
          fileName = fileName.concat(Date.now().toString());
          fileName = fileName.concat('.' + extension);
          file = bucket.file(fileName ?? nameData);
        }
        file.save(bufferData, { validation: 'md5' }, function (error) {
          if (error) reject(error);
          else if (!file.publicUrl())
            reject(new Error('File URL could not be generated'));
          else resolve({ url: file.publicUrl() });
        });
      });
      this.removeFile(filePath);
      return url['url'];
    } catch (error) {
      this.removeFile(filePath);
      HTTPError({ statusCode: HttpStatus.INTERNAL_SERVER_ERROR });
    }
  }

  private async uploadOracleCloudFile(
    filePath: string,
    folderName?: string,
    extension?: string,
    bucketName?: string,
  ): Promise<any> {
    try {
      const file = filePath.replace('./', '');
      const content = await fs.promises.readFile(filePath);
      const month = new Date().toLocaleString('default', { month: 'short' });
      const year = new Date().getFullYear();
      const objectName =
        (folderName ?? 'DEFAULT') + '/' + `${month}-${year}` + '/' + file;

      let fileType = extension ?? null;
      if (!extension) {
        const match = file.match(/\.([a-zA-Z0-9]+)(?:\?.*)?$/);
        fileType = match ? match[1] : '';
      }

      const putObjectRequest: requests.PutObjectRequest = {
        namespaceName: Env.cloud.oracle.namespace,
        bucketName: bucketName ?? Env.cloud.oracle.bucket,
        putObjectBody: content,
        objectName,
        contentType: kMimeTypes[fileType],
        opcMeta: {
          'Content-Disposition': 'inline',
        },
      };

      ///upload file
      await this.objectStorageClient.putObject(putObjectRequest);

      // remove file
      this.removeFile(filePath);

      ///manage PAR
      return await this.getPARUrlDetails(objectName);
    } catch (error) {
      this.removeFile(filePath);
      HTTPError({ statusCode: HttpStatus.INTERNAL_SERVER_ERROR });
    }
  }

  private async getPARUrlDetails(filePath?: string) {
    const expirationDate = new Date();
    expirationDate.setFullYear(expirationDate.getFullYear() + 100);

    const createPARRequest: requests.CreatePreauthenticatedRequestRequest = {
      namespaceName: Env.cloud.oracle.namespace,
      bucketName: Env.cloud.oracle.bucket,
      createPreauthenticatedRequestDetails: {
        name: `par_for_${filePath}`,
        objectName: filePath,
        accessType: 'ObjectReadWrite' as any,
        timeExpires: expirationDate,
      },
    };
    const parResponse =
      await this.objectStorageClient.createPreauthenticatedRequest(
        createPARRequest,
      );
    return `https://objectstorage.ap-mumbai-1.oraclecloud.com${parResponse.preauthenticatedRequest.accessUri}`;
  }

  async objectToExcelURL(
    rawData: any,
    extraParam: any = false,
    isCrif = false,
    folderName = CLOUD_FOLDER.reports,
  ) {
    const excelData: any = await this.objectToExcel(
      rawData,
      extraParam,
      isCrif,
    );
    const filePath = excelData.filePath;

    let fileName: any;
    if (rawData.reportStore == true) {
      let startDate = this.dateService.dateToJsonStr(
        rawData.startDate ?? new Date(),
      );
      let endDate = this.dateService.dateToJsonStr(
        rawData.endDate ?? new Date(),
      );
      fileName = excelData.filePath.split('/')[2] ?? '';
      fileName = fileName.replace(/ /g, '_') ?? '';
      if (startDate && endDate) {
        fileName = fileName.split('.');
        fileName = `${fileName[0]}_${startDate}_to_${endDate}.${fileName[1]}`;
      }
    }
    const urlData = await this.uploadFile(
      filePath,
      folderName,
      'xlsx',
      fileName,
    );
    return urlData;
  }

  async objectToExcel(rawData, extraParam: any = false, isCrif = false) {
    if (!rawData.sheets || !rawData.data) raiseParamMissing('sheets');
    if (!rawData.data) raiseParamMissing('data');

    const workbook = new excel.Workbook();
    const bigFonts = workbook.createStyle(kFonts.big);
    const smallFonts = workbook.createStyle(kFonts.small);

    // Check if rawData.sheets is an array (multiple sheets) or a string (single sheet)
    if (Array.isArray(rawData.sheets)) {
      // Handle multiple sheets
      for (
        let sheetIndex = 0;
        sheetIndex < rawData.sheets.length;
        sheetIndex++
      ) {
        const currentSheet = workbook.addWorksheet(rawData.sheets[sheetIndex]);
        const sheetDetails = rawData.data[sheetIndex];
        this.writeSheetData(
          currentSheet,
          sheetDetails,
          bigFonts,
          smallFonts,
          extraParam,
        );
      }
    } else if (typeof rawData.sheets === 'string') {
      // Handle single sheet
      const currentSheet = workbook.addWorksheet(rawData.sheets);
      const sheetDetails = rawData.data;
      this.writeSheetData(
        currentSheet,
        sheetDetails,
        bigFonts,
        smallFonts,
        extraParam,
      );
    } else raiseBadRequest('Invalid sheets data');
    const sheetName = rawData?.sheetName;
    const today = new Date();
    const date = this.dateService.dateToJsonStr(today);
    const dateTime = `${today.getHours().toString().padStart(2, '0')}-${today.getMinutes().toString().padStart(2, '0')}`;
    const extension = !(sheetName && sheetName.includes('.xlsx'))
      ? '.xlsx'
      : '';
    const currentTime = new Date().getTime();
    let filePath = sheetName
      ? `upload/report/${date}-${currentTime}-${dateTime}-${sheetName}${extension}`
      : 'upload/file.xlsx';
    if (isCrif) filePath = `upload/report/reports_${currentTime}.xlsx`;
    return await new Promise((resolve, reject) => {
      workbook.write(filePath, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve({ filePath });
        }
      });
    });
  }

  private writeSheetData(
    sheet,
    sheetDetails,
    bigFonts,
    smallFonts,
    extraParam: any = false,
  ) {
    const columnWidths = [];
    // Iterate through rows in sheetDetails to find maximum content length for each column
    for (let rowIndex = 0; rowIndex < sheetDetails.length; rowIndex++) {
      const rowDetails = sheetDetails[rowIndex];
      let columnIndex = 1;

      // Iterate through key-value pairs in each row
      for (const [_, value] of Object.entries(rowDetails)) {
        // Calculate cell content length
        let cellContentLength: any = '';
        if (extraParam) cellContentLength = value.toString().length;
        else cellContentLength = (value ?? '-').toString().length;

        // Update column width if the current content length is greater than the stored width
        columnWidths[columnIndex - 1] = Math.max(
          columnWidths[columnIndex - 1] || 0,
          cellContentLength,
        );
        columnIndex++;
      }
    }
    // Set column widths based on the maximum content length in each column
    for (let i = 0; i < columnWidths.length; i++) {
      const width = Math.max(columnWidths[i] * 1.75, 10); // Minimum width of 10 for readability
      sheet.column(i + 1).setWidth(width);
    }

    // Write the data to the sheet with proper formatting
    for (let rowIndex = 0; rowIndex < sheetDetails.length; rowIndex++) {
      const rowDetails = sheetDetails[rowIndex];
      let columnIndex = 1;

      for (const [key, value] of Object.entries(rowDetails)) {
        // Write header row (first row) with big fonts style
        if (rowIndex === 0)
          sheet.cell(1, columnIndex).string(key).style(bigFonts);

        // Write cell values with small fonts style
        let cellValue: any = value ?? '-';

        if (extraParam) {
          // If the cell value is an empty string, skip the rest and leave the cell blank
          if (cellValue === '') {
            columnIndex++;
            continue;
          }
        } else {
          if (cellValue == '') cellValue = '-';
        }
        let tempVal = cellValue;
        if (typeof tempVal == 'string') {
          tempVal = tempVal.includes(kRupee)
            ? tempVal.replace(/[₹,]/g, '')
            : tempVal.includes(',')
              ? tempVal.replace(/,/g, '')
              : cellValue;
          cellValue = !isNaN(tempVal) ? +tempVal : cellValue;
        }
        if (typeof cellValue == 'number')
          sheet
            .cell(rowIndex + 2, columnIndex)
            .number(cellValue)
            .style(smallFonts);
        else
          sheet
            .cell(rowIndex + 2, columnIndex)
            .string(cellValue.toString())
            .style(smallFonts);

        columnIndex++;
      }
    }
  }

  async excelToArray(
    filePath,
    customColumns = {},
    needColumnName = false,
    empty?,
    date = true,
  ) {
    try {
      if (!filePath.endsWith('.xlsx') && !filePath.endsWith('.csv')) {
        throw HTTPError({
          message: 'Invalid file type. Only XLSX and CSV files are supported',
        });
      }
      const Excel = require('exceljs');
      const workbook = new Excel.Workbook();
      const readFileMethod = filePath.endsWith('.xlsx') ? 'xlsx' : 'csv';
      await workbook[readFileMethod].readFile(filePath);

      let worksheet = workbook.getWorksheet(1);
      if (!worksheet) worksheet = workbook.getWorksheet();
      const finalizedArray = [];
      const keyArr = [];
      const allColumnName = [];
      // Get column names
      worksheet.getRow(1).eachCell((cell, colNumber) => {
        try {
          const columnName = cell.value;
          keyArr[colNumber] = customColumns[columnName]
            ? customColumns[columnName]
            : columnName;
          allColumnName.push(keyArr[colNumber]);
        } catch (error) {}
      });

      for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
        const row = worksheet.getRow(rowNumber);
        const rowData = {};
        try {
          row.eachCell((cell, colNumber) => {
            const columnName = keyArr[colNumber];
            let cellVal = cell.value;
            if (cellVal instanceof Date && !date) {
              const day = String(cellVal.getDate());
              const month = String(cellVal.getMonth() + 1);
              const year = cellVal.getFullYear();
              cellVal = `${day}-${month}-${year}`;
            }
            rowData[columnName] = cellVal;
          });
          finalizedArray.push(rowData);
        } catch (error) {}
      }
      if (needColumnName)
        return { columnName: allColumnName, finalData: finalizedArray };
      return finalizedArray;
    } catch (error) {
      if (empty == true) {
        if (error.message.includes('End of data reached (data length = 0')) {
          return [];
        }
      }
      return [];
    }
  }

  private async fileUrlToBase64(fileUrl: string): Promise<string> {
    const response = await axios.get(fileUrl, {
      responseType: 'arraybuffer',
    });
    const fileContent = response.data;
    const base64 = Buffer.from(fileContent).toString('base64');
    return base64;
  }

  private base64ToFile(base64Content, extension = 'pdf') {
    const filePath = './' + new Date().getTime().toString() + '.' + extension;
    fs.writeFileSync(filePath, base64Content, 'base64');
    return filePath;
  }

  async fileUrlToFile(fileUrl: string): Promise<string> {
    const base64 = await this.fileUrlToBase64(fileUrl);
    const splittedSpans = fileUrl.split('.');
    const extension = splittedSpans[splittedSpans.length - 1];
    return this.base64ToFile(base64, extension);
  }
}
