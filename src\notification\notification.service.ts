import { HttpStatus, Injectable } from '@nestjs/common';
import { HTTPError } from 'src/config/error';
import { NUMBERS } from 'src/constant/objects';
import { TemplateEntity } from 'src/database/pg/entities/template.entity';
import { PgService } from 'src/database/pg/pg.service';
import { RedisService } from 'src/database/redis/redis.service';

@Injectable()
export class NotificationService {
  constructor(
    private readonly pg: PgService,
    private readonly redisService: RedisService,
  ) {}

  async getTemplatesList() {
    const redisKey = 'CHAT_MSG_TEMPLATES';
    const redisData = await this.redisService.getString(redisKey);
    if (redisData) return JSON.parse(redisData);
    const where: any = { type: 'CHAT_SUPPORT' };
    const templateData = await this.pg.findAll(TemplateEntity, {
      where,
      attributes: ['id', 'title', 'content'],
      order: [['title']],
    });
    if (!templateData)
      throw new HTTPError({
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'No Data Found',
      });
    await this.redisService.setString(
      redisKey,
      JSON.stringify(templateData),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );
    return templateData;
  }
}
