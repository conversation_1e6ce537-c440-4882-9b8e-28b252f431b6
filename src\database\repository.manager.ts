import { Injectable } from '@nestjs/common';
import { PgService } from './pg/pg.service';

@Injectable()
export class RepositoryManager {
  constructor(private readonly pg: PgService) {}
  async create(repository: any, data: any) {
    return await this.pg.create(repository, data);
  }

  async getTableWhereData(repository: any, options: any) {
    return await this.pg.findAll(repository, options);
  }

  async update(repository: any, dataToUpdate: any, options) {
    return await this.pg.update(repository, dataToUpdate, options);
  }

  async getRowWhereData(repository: any, options: any) {
    return await this.pg.findOne(repository, options);
  }
}
