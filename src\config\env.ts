// Imports
import {
  backendServiceConfigs,
  callKaroConfigs,
  clickhouseConfigs,
  cryptConfigs,
  dataCodesConfigs,
  exotelConfigs,
  experianConfigs,
  googleCloudConfig,
  microAlertConfigs,
  oracleCloudConfig,
  postgresqlConfigs,
  redisConfigs,
  serverConfigs,
  transactionConfigs,
  trueShieldConfigs,
} from './configuration';

export const Env = {
  server: {
    port: serverConfigs.port,
  },
  database: {
    clickhouse: {
      url: clickhouseConfigs.url,
      port: clickhouseConfigs.port,
      database: clickhouseConfigs.database,
      username: clickhouseConfigs.username,
      password: clickhouseConfigs.password,
    },
    postgresql: {
      core_db_name: postgresqlConfigs.core_db_name,
      fin360_db_name: postgresqlConfigs.fin360_db_name,
      ind_bank_db_name: postgresqlConfigs.ind_bank_db_name,
      sms_db_name: postgresqlConfigs.sms_db_name,
    },
    redis: {
      host: redisConfigs.host,
      password: redisConfigs.password,
      port: redisConfigs.port,
      prefix: redisConfigs.prefix,
      client: redisConfigs.client,
    },
  },

  crypt: {
    sysEncKey: cryptConfigs.sysEncKey,
    phoneEncKey: cryptConfigs.phoneEncKey,
  },

  neighbours: {
    backendService: {
      base_url: backendServiceConfigs.base_url,
      qa_test_key: backendServiceConfigs['qa-test-key'],
    },
    dataCodes: {
      base_url: dataCodesConfigs.base_url,
      username: dataCodesConfigs.username,
      password: dataCodesConfigs.password,
    },
    microAlert: { base_url: microAlertConfigs.base_url },
    transactions: { base_url: transactionConfigs.base_url },
    trueShield: {
      auth_key: trueShieldConfigs.auth_key,
      base_url: trueShieldConfigs.base_url,
    },
  },

  thirdParty: {
    callKaro: {
      partner_key: callKaroConfigs['partner-key'],
      white_listed_ips: callKaroConfigs['white-listed-ips'],
    },
    experian: { 'nbfc-prefix': experianConfigs['nbfc-prefix'] },
    exotel: {
      sid: exotelConfigs.sid,
      api_key: exotelConfigs.api_key,
      base_url: exotelConfigs.base_url,
      api_token: exotelConfigs.api_token,
      support_contact_1: exotelConfigs.support_contact_1,
      support_contact_2: exotelConfigs.support_contact_2,
    },
  },
  cloud: {
    google: {
      projectName: googleCloudConfig.projectName,
      bucketName: process.env.CLOUD_BUCKET_NAME,
      appCredentials: process.env.GOOGLE_APPLICATION_CREDENTIALS,
    },
    oracle: {
      user: oracleCloudConfig.user,
      fingerprint: oracleCloudConfig.fingerprint,
      tenancy: oracleCloudConfig.tenancy,
      namespace: oracleCloudConfig.namespace,
      bucket: oracleCloudConfig.bucket,
      publicBucket: oracleCloudConfig.publicBucket,
    },
  },
};
