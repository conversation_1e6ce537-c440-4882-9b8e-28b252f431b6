import { Controller, Get, Query } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';

@Controller('admin/analytics')
export class AnalyticsController {
  constructor(private readonly service: AnalyticsService) {}

  // EMI Repaid Analytics APR Wise
  @Get('emiRepaidAnalytics')
  async funEmiRepaidCount(@Query() query) {
    return await this.service.funCompEmiAnalytics(query);
  }

  // Loan Disbursed Analytics APR Wise
  @Get('loanDisbursedAnalytics')
  async funLoandisbursedCount(@Query() query) {
    return await this.service.funDisbursLoanAnalytics(query);
  }
}
