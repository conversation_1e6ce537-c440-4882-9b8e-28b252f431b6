// Imports
import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { CommonController } from './common.controller';
import { RepositoryManager } from 'src/database/repository.manager';
import { PgService } from 'src/database/pg/pg.service';
import { NotificationService } from 'src/notification/notification.service';
import { RedisModule } from 'src/database/redis/redis.module';
import { CommonService } from './common.services';

@Module({
  controllers: [CommonController],
  imports: [RedisModule],
  providers: [NotificationService, PgService, RepositoryManager, CommonService],
  exports: [CommonService],
})
export class CommonModule {}
