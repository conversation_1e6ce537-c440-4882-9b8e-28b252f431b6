// Imports
import { Env } from 'src/config/env';
import { Module } from '@nestjs/common';
import { PgService } from './pg.service';
import { SequelizeModule } from '@nestjs/sequelize';
import { postgresqlConfigs } from 'src/config/configuration';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  FIN_360_ENTITIES,
  PG_CORE_ENTITIES,
  IND_BANK_ENTITIES,
  SMS_ENTITIES,
} from './pg.entities';

const getDbConnection = (db_name, models) =>
  SequelizeModule.forRootAsync({
    imports: [ConfigModule],
    name: db_name,
    useFactory: (_: ConfigService) => ({
      ...postgresqlConfigs,
      database: db_name,
      synchronize: false,
      define: {
        freezeTableName: false,
        charset: 'utf8',
        collate: 'utf8_general_ci',
      },
      models,
    }),
    inject: [ConfigService],
  });

@Module({
  imports: [
    getDbConnection(Env.database.postgresql.core_db_name, PG_CORE_ENTITIES),

    getDbConnection(Env.database.postgresql.fin360_db_name, FIN_360_ENTITIES),

    getDbConnection(
      Env.database.postgresql.ind_bank_db_name,
      IND_BANK_ENTITIES,
    ),

    getDbConnection(Env.database.postgresql.sms_db_name, SMS_ENTITIES),
  ],
  providers: [PgService],
  exports: [PgService],
  controllers: [],
})
export class PgModule {}
