// Imports
import { Injectable } from '@nestjs/common';
import {
  HTTPError,
  raiseBadRequest,
  raiseParamInvalid,
  raiseParamMissing,
} from 'src/config/error';
import { kGlobalDateTrail } from 'src/constant/global';
import { NumberService } from './number.service';

@Injectable()
export class DateService {
  constructor(private readonly num: NumberService) {}

  static aadhaarDateStrToDOBDate(dateOfBirth) {
    if (dateOfBirth.includes('/')) {
      const modifiedDOB = dateOfBirth.split('/').reverse().join('-');
      const aadharDOB = modifiedDOB.split('-');

      aadharDOB[1] = this.addLeadingZero(parseInt(aadharDOB[1], 10));
      aadharDOB[2] = this.addLeadingZero(parseInt(aadharDOB[2], 10));

      return this.getGlobalDate(
        new Date(aadharDOB[0] + '-' + aadharDOB[1] + '-' + aadharDOB[2]),
      );
    }
    return this.getGlobalDate(new Date(dateOfBirth));
  }

  private static getGlobalDate(experimentDate: Date) {
    const currentDate = new Date(experimentDate);
    currentDate.setMinutes(currentDate.getMinutes() + 330);
    const currentStatic =
      currentDate.toJSON().substring(0, 10) + kGlobalDateTrail;

    return new Date(currentStatic);
  }

  private static addLeadingZero(number) {
    return number < 10 ? `0${number}` : `${number}`;
  }

  static difference(nextDate: Date, currentdate: Date, type = 'Days') {
    let fromTime;
    let toTime;
    if (type == 'Days' || type === 'Years') {
      fromTime = this.getGlobalDate(currentdate).getTime();
      toTime = this.getGlobalDate(nextDate).getTime();
    } else {
      fromTime = currentdate.getTime();
      toTime = nextDate.getTime();
    }
    let difference = fromTime - toTime;
    difference = Math.abs(difference);
    if (type == 'Seconds')
      difference = Math.floor(Math.ceil(difference / (1000 * 60)) * 60);
    else if (type == 'Minutes')
      difference = Math.ceil(difference / (1000 * 60));
    else if (type == 'Hours')
      difference = Math.ceil(difference / (1000 * 3600));
    else if (type == 'Days')
      difference = Math.ceil(difference / (1000 * 3600 * 24));
    else if (type == 'Month')
      difference = Math.ceil(difference / (1000 * 3600 * 24 * 30));
    else if (type == 'Years')
      difference = Math.floor(Math.ceil(difference / (1000 * 3600 * 24)) / 365);
    return difference;
  }

  getGlobalDate(experimentDate: Date) {
    const currentDate = new Date(experimentDate);
    currentDate.setMinutes(currentDate.getMinutes() + 330);
    const currentStatic =
      currentDate.toJSON().substring(0, 10) + kGlobalDateTrail;

    return new Date(currentStatic);
  }

  getTodayGlobalDate() {
    return this.getGlobalDate(new Date());
  }

  strToDate(dateString: string, dateFormat: 'DDMMYYYY' | 'YYYYMMDD'): Date {
    if (dateFormat == 'DDMMYYYY') {
      const ddStr = dateString.substring(0, 2);
      const mmStr = dateString.substring(2, 4);
      const yyyyStr = dateString.substring(4);

      const target_date_str = `${yyyyStr}-${mmStr}-${ddStr}${kGlobalDateTrail}`;
      return new Date(target_date_str);
    } else if (dateFormat == 'YYYYMMDD') {
      const ddStr = dateString.substring(6);
      const mmStr = dateString.substring(4, 6);
      const yyyyStr = dateString.substring(0, 4);

      const target_date_str = `${yyyyStr}-${mmStr}-${ddStr}${kGlobalDateTrail}`;
      return new Date(target_date_str);
    } else {
      throw HTTPError({});
    }
  }

  difference(nextDate: Date, currentdate: Date, type = 'Days') {
    let fromTime;
    let toTime;
    if (type == 'Days' || type === 'Years') {
      fromTime = this.getGlobalDate(currentdate).getTime();
      toTime = this.getGlobalDate(nextDate).getTime();
    } else {
      fromTime = currentdate.getTime();
      toTime = nextDate.getTime();
    }
    let difference = fromTime - toTime;
    difference = Math.abs(difference);
    if (type == 'Seconds')
      difference = Math.floor(Math.ceil(difference / (1000 * 60)) * 60);
    else if (type == 'Minutes')
      difference = Math.ceil(difference / (1000 * 60));
    else if (type == 'Hours')
      difference = Math.ceil(difference / (1000 * 3600));
    else if (type == 'Days')
      difference = Math.ceil(difference / (1000 * 3600 * 24));
    else if (type == 'Month')
      difference = Math.ceil(difference / (1000 * 3600 * 24 * 30));
    else if (type == 'Years')
      difference = Math.floor(Math.ceil(difference / (1000 * 3600 * 24)) / 365);
    return difference;
  }

  aadhaarDateStrToDOBDate(dateOfBirth) {
    if (dateOfBirth.includes('/')) {
      const modifiedDOB = dateOfBirth.split('/').reverse().join('-');
      const aadharDOB = modifiedDOB.split('-');

      aadharDOB[1] = this.addLeadingZero(parseInt(aadharDOB[1], 10));
      aadharDOB[2] = this.addLeadingZero(parseInt(aadharDOB[2], 10));

      return this.getGlobalDate(
        new Date(aadharDOB[0] + '-' + aadharDOB[1] + '-' + aadharDOB[2]),
      );
    }
    return this.getGlobalDate(new Date(dateOfBirth));
  }

  minutesToFormattedStr(totalMinutes: number) {
    const absTotal = Math.abs(totalMinutes);
    const mins = absTotal % 60;
    const hours = Math.floor(absTotal / 60);
    const days = Math.floor(hours / 24);
    const hourss = hours % 24;
    return days + 'd, ' + hourss + 'h, ' + mins + 'm';
  }

  getDateRangeFromMonthYear(monthYear: string): {
    start_date: string;
    end_date: string;
  } {
    const monthMap: Record<string, number> = {
      Jan: 0,
      Feb: 1,
      Mar: 2,
      Apr: 3,
      May: 4,
      Jun: 5,
      Jul: 6,
      Aug: 7,
      Sep: 8,
      Oct: 9,
      Nov: 10,
      Dec: 11,
    };

    // Expect format like "Apr-24"
    const match = monthYear.match(/^([A-Za-z]{3})-(\d{2})$/);
    if (!match) {
      throw new Error(
        `Invalid monthYear format: ${monthYear}. Expected format is Mon-YY, e.g., Apr-24.`,
      );
    }

    const monthAbbr = match[1];
    const yearTwoDigit = parseInt(match[2], 10);

    if (!(monthAbbr in monthMap)) {
      throw new Error(`Invalid month abbreviation: ${monthAbbr}`);
    }

    // Year: Assume 00-68 means 2000-2068, and 69-99 means 1969-1999 (like POSIX).
    const fullYear = yearTwoDigit + (yearTwoDigit >= 69 ? 1900 : 2000);

    const month = monthMap[monthAbbr];

    // Start date: first day of the month at 10:00:00 UTC
    const startDate = new Date(Date.UTC(fullYear, month, 1, 10, 0, 0));

    // End date: last day of the month at 10:00:00 UTC
    const endDate = new Date(Date.UTC(fullYear, month + 1, 0, 10, 0, 0));

    return {
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
    };
  }

  private addLeadingZero(number) {
    return number < 10 ? `0${number}` : `${number}`;
  }

  private isValidDate(date: any): Boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }

  istDateRange(minDate: Date | String, maxDate: Date | String) {
    if (!minDate) raiseParamMissing('minDate');
    if (!maxDate) raiseParamMissing('maxDate');
    if ((typeof minDate === 'string') !== (typeof maxDate === 'string'))
      raiseBadRequest('minDate and maxDate must be of the same type');

    const date1: Date =
      typeof minDate === 'string' ? new Date(minDate) : (minDate as Date);
    if (!this.isValidDate(date1)) raiseParamInvalid('minDate');

    const date2: Date =
      typeof maxDate === 'string' ? new Date(maxDate) : (maxDate as Date);
    if (!this.isValidDate(date2)) raiseParamInvalid('maxDate');

    if (date2.getTime() < date1.getTime())
      raiseBadRequest('minDate must be less than or equal to maxDate');

    date1.setDate(date1.getDate() - 1);
    date1.setHours(23);
    date1.setMinutes(60);
    date1.setSeconds(0);

    date2.setHours(23);
    date2.setMinutes(60);
    date2.setSeconds(0);

    return {
      minRange: date1,
      maxRange: date2,
    };
  }

  getIstDate(utcDate: Date | String) {
    if (!utcDate) raiseParamMissing('minDate');
    const date1: Date =
      typeof utcDate === 'string' ? new Date(utcDate) : (utcDate as Date);
    if (!this.isValidDate(date1)) raiseParamInvalid('utcDate');
    date1.setMinutes(date1.getMinutes() + 330);
    return new Date(date1);
  }

  dateToJsonStr(targetDate: Date, format = 'DD-MM-YYYY', connector = '-') {
    if (!this.isValidDate(targetDate)) raiseParamInvalid('minDate');
    const date = targetDate.getDate().toString().padStart(2, '0');
    const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');
    const year = targetDate.getFullYear().toString().padStart(4, '0');
    if (format == 'DD-MM-YYYY')
      return `${date}${connector}${month}${connector}${year}`;
    else if (format == 'YYYY-MM-DD')
      return `${year}${connector}${month}${connector}${date}`;
    return `${date}-${month}-${year}`;
  }

  utcDateRange(minDate: Date, maxDate?: Date) {
    const start = new Date(typeof minDate === 'string' ? minDate : minDate);
    const end = new Date(
      typeof maxDate === 'string' ? maxDate : (maxDate ?? start),
    );

    start.setUTCHours(0, 0, 0, 0);
    end.setUTCHours(23, 59, 59, 999);

    return {
      start,
      end,
    };
  }

  // 2024-01-22T10:05:21.665Z -> 22 Jan 2024 03:35 PM
  dateToReadableFormat(dateString: string, isSecond = false, dateType = 'utc') {
    const date = new Date(dateString);

    // Convert the time to UTC if in IST format....
    if (dateType == 'ist') {
      date.setMinutes(date.getMinutes() - 330);
    }
    const month = date.toLocaleString('default', { month: 'short' });
    const pad = (n: number) => String(n).padStart(2, '0');
    const year = date.getFullYear();
    const day = date.getDate();
    let hours = date.getHours();
    hours = hours % 12 || 12;
    const meridiem = hours >= 12 ? 'PM' : 'AM';

    const minutes = date.getMinutes();
    const time = `${pad(hours)}:${pad(minutes)}${isSecond ? `:${pad(date.getSeconds())}` : ''}`;
    return `${day} ${month} ${year} ${time} ${meridiem}`;
  }

  formatDateToLongMonth(dateString: string): string {
    const date = new Date(dateString);
    const day = date.getUTCDate();
    const month = date.toLocaleString('en-GB', {
      month: 'long',
      timeZone: 'UTC',
    });
    return `${this.num.convertNumberToWords(day)} ${month}`;
  }

  // 2024-01-22T10:05:21.665Z ->> 17 Jul 2025 06:22:09 PM
  convertToReadableIST(dateString: string) {
    const date = new Date(dateString)
      .toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
      })
      .replace(',', '')
      .replace('am', 'AM')
      .replace('pm', 'PM');

    return date;
  }
}
