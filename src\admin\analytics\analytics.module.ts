import { Module } from '@nestjs/common';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';
import { PgService } from 'src/database/pg/pg.service';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  imports: [UtilsModule],
  controllers: [AnalyticsController],
  providers: [AnalyticsService, PgService],
})
export class AnalyticsModule {}
