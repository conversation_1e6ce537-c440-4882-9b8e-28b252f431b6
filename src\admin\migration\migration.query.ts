// Imports
import { Includeable, Op } from 'sequelize';
import { Injectable } from '@nestjs/common';
import { raiseParamMissing } from 'src/config/error';
import { PgService } from 'src/database/pg/pg.service';
import { kGlobalDateTrail } from 'src/constant/global';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { PredictionEntity } from 'src/database/pg/entities/prediction.entity';
import { ClickhouseService } from 'src/database/clickhouse/clickhouse.service';

@Injectable()
export class MigrationQuery {
  constructor(
    private readonly pg: PgService,
    private readonly clickHouse: ClickhouseService,
  ) {}

  async dataForMissingInternalScore() {
    const predictionInc: Includeable = { model: PredictionEntity };
    predictionInc.attributes = ['id'];
    predictionInc.where = { CFLScore: { [Op.eq]: null } };

    const loanList = await this.pg.findAll(loanTransaction, {
      attributes: ['id'],
      include: predictionInc,
      order: [['id', 'DESC']],
      where: {
        loan_disbursement_date: { [Op.gte]: '2025-01-01' + kGlobalDateTrail },
      },
      limit: 1000,
    });

    return { count: loanList.length, rows: loanList };
  }

  async dataForMissingLeadScore(reqData) {
    const stage = reqData.stage;
    if (!stage) {
      raiseParamMissing('stage');
    }
    const limit = reqData.limit;
    if (!limit) {
      raiseParamMissing('limit');
    }

    const query = `SELECT "user_id" FROM "user_details"
    
    WHERE "lead_score" IS NULL AND "stage" = ${stage}
    
    ORDER BY "created_at" DESC
    
    LIMIT ${limit}`;

    const output: any = await this.clickHouse.injectQuery(query);

    return output.map((el) => el.user_id);
  }
}
